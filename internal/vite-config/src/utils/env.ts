import type { ApplicationPluginOptions } from '../typing';

import { existsSync } from 'node:fs';
import { join } from 'node:path';

import { fs } from '@vben/node-utils';

import dotenv from 'dotenv';

const getBoolean = (value: string | undefined) => value === 'true';

const getString = (value: string | undefined, fallback: string) =>
  value ?? fallback;

const getNumber = (value: string | undefined, fallback: number) =>
  Number(value) || fallback;

/**
 * 获取当前环境下生效的配置文件名
 */
function getConfFiles() {
  const script = process.env.npm_lifecycle_script as string;
  const reg = /--mode ([\d_a-z]+)/;
  const result = reg.exec(script);
  let mode = 'production';
  if (result) {
    mode = result[1] as string;
  }
  return ['.env', '.env.local', `.env.${mode}`, `.env.${mode}.local`];
}

/**
 * Get the environment variables starting with the specified prefix
 * @param match prefix
 * @param confFiles ext
 */
async function loadEnv<T = Record<string, string>>(
  match = 'VITE_GLOB_',
  confFiles = getConfFiles(),
) {
  let envConfig = {};

  for (const confFile of confFiles) {
    try {
      const confFilePath = join(process.cwd(), confFile);
      if (existsSync(confFilePath)) {
        const envPath = await fs.readFile(confFilePath, {
          encoding: 'utf8',
        });
        const env = dotenv.parse(envPath);
        envConfig = { ...envConfig, ...env };
      }
    } catch (error) {
      console.error(`Error while parsing ${confFile}`, error);
    }
  }
  const reg = new RegExp(`^(${match})`);
  Object.keys(envConfig).forEach((key) => {
    if (!reg.test(key)) {
      Reflect.deleteProperty(envConfig, key);
    }
  });
  return envConfig as T;
}

async function loadAndConvertEnv(
  match = 'VITE_',
  confFiles = getConfFiles(),
): Promise<
  Partial<ApplicationPluginOptions> & {
    appTitle: string;
    base: string;
    port: number;
  }
> {
  const envConfig = await loadEnv(match, confFiles);

  const {
    VITE_APP_TITLE,
    VITE_ARCHIVER,
    VITE_BASE,
    VITE_COMPRESS,
    VITE_DEVTOOLS,
    VITE_INJECT_APP_LOADING,
    VITE_NITRO_MOCK,
    VITE_PORT,
    VITE_PWA,
    VITE_VISUALIZER,
  } = envConfig;

  const compressTypes = (VITE_COMPRESS ?? '')
    .split(',')
    .filter((item) => item === 'brotli' || item === 'gzip');

  return {
    appTitle: getString(VITE_APP_TITLE, 'Joy Admin'),
    archiver: getBoolean(VITE_ARCHIVER),
    base: getString(VITE_BASE, '/'),
    compress: compressTypes.length > 0,
    compressTypes,
    devtools: getBoolean(VITE_DEVTOOLS),
    injectAppLoading: getBoolean(VITE_INJECT_APP_LOADING),
    nitroMock: getBoolean(VITE_NITRO_MOCK),
    port: getNumber(VITE_PORT, 5173),
    pwa: getBoolean(VITE_PWA),
    visualizer: getBoolean(VITE_VISUALIZER),
  };
}

/**
 * 创建代理配置
 * @param proxyList 代理配置列表，格式：[["/api", "http://localhost:3000"], ["/upload", "http://localhost:3001"]]
 * @returns Vite 代理配置对象
 */
function createProxy(proxyList: Array<[string, string]> = []) {
  const proxy: Record<string, any> = {};

  for (const [prefix, target] of proxyList) {
    const isHttps = /^https:\/\//.test(target);

    proxy[prefix] = {
      // target: target + prefix,
      target:
        target +
        `${prefix == '/admin-api' ? prefix : prefix.replace('/admin-api', '')}`,
      changeOrigin: true,
      ws: true,
      rewrite: (path: string) => path.replace(new RegExp(`^${prefix}`), ''),
      // https 需要设置 secure: false
      ...(isHttps ? { secure: false } : {}),
    };
  }

  return proxy;
}

/**
 * 从环境变量中解析代理配置
 * @param envConfig 环境变量配置对象
 * @returns Vite 代理配置对象
 */
function createProxyFromEnv(envConfig: Record<string, any>) {
  console.log('🌍 [createProxyFromEnv] 开始从环境变量创建代理配置');
  console.log('📝 [createProxyFromEnv] 环境配置:', envConfig);

  const { VITE_PROXY } = envConfig;
  console.log('🔍 [createProxyFromEnv] VITE_PROXY 值:', VITE_PROXY);

  if (!VITE_PROXY) {
    console.log('⚠️ [createProxyFromEnv] VITE_PROXY 未设置，返回空对象');
    return {};
  }

  try {
    console.log('🔄 [createProxyFromEnv] 开始解析 VITE_PROXY 字符串');
    // 解析 VITE_PROXY 字符串，支持格式：[["/api","http://localhost:3000"],["/upload","http://localhost:3001"]]
    const proxyList = JSON.parse(VITE_PROXY);
    console.log('✅ [createProxyFromEnv] VITE_PROXY 解析成功:', proxyList);

    if (!Array.isArray(proxyList)) {
      console.warn('❌ [createProxyFromEnv] VITE_PROXY should be an array');
      return {};
    }

    console.log('🚀 [createProxyFromEnv] 调用 createProxy 创建代理配置');
    const result = createProxy(proxyList);
    console.log('🎯 [createProxyFromEnv] 代理配置创建完成，返回结果');
    return result;
  } catch (error) {
    console.error('💥 [createProxyFromEnv] Failed to parse VITE_PROXY:', error);
    return {};
  }
}

export { createProxy, createProxyFromEnv, loadAndConvertEnv, loadEnv };
