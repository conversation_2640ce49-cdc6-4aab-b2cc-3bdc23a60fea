{"name": "@vben/locales", "version": "5.5.7", "homepage": "", "bugs": "", "repository": {"type": "git", "url": "", "directory": "packages/locales"}, "license": "MIT", "type": "module", "sideEffects": ["**/*.css"], "exports": {".": {"types": "./src/index.ts", "default": "./src/index.ts"}}, "dependencies": {"@intlify/core-base": "catalog:", "@vben-core/composables": "workspace:*", "vue": "catalog:", "vue-i18n": "catalog:"}}