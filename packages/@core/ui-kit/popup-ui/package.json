{"name": "@vben-core/popup-ui", "version": "5.2.1", "homepage": "", "bugs": "", "repository": {"type": "git", "url": "", "directory": "packages/@vben-core/uikit/popup-ui"}, "license": "MIT", "type": "module", "scripts": {"build": "pnpm unbuild", "prepublishOnly": "npm run build"}, "files": ["dist"], "sideEffects": ["**/*.css"], "main": "./dist/index.mjs", "module": "./dist/index.mjs", "exports": {".": {"types": "./src/index.ts", "development": "./src/index.ts", "default": "./dist/index.mjs"}}, "publishConfig": {"exports": {".": {"default": "./dist/index.mjs"}}}, "dependencies": {"@vben-core/composables": "workspace:*", "@vben-core/icons": "workspace:*", "@vben-core/shadcn-ui": "workspace:*", "@vben-core/shared": "workspace:*", "@vben-core/typings": "workspace:*", "@vueuse/core": "catalog:", "vue": "catalog:"}}