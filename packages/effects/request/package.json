{"name": "@vben/request", "version": "5.5.7", "homepage": "", "bugs": "", "repository": {"type": "git", "url": "", "directory": "packages/effects/request"}, "license": "MIT", "type": "module", "sideEffects": ["**/*.css"], "exports": {".": {"types": "./src/index.ts", "default": "./src/index.ts"}}, "dependencies": {"@microsoft/fetch-event-source": "catalog:", "@vben/locales": "workspace:*", "@vben/utils": "workspace:*", "axios": "catalog:", "qs": "catalog:"}, "devDependencies": {"@types/qs": "catalog:", "axios-mock-adapter": "catalog:"}}