{"name": "@vben/plugins", "version": "5.5.6", "homepage": "", "bugs": "", "repository": {"type": "git", "url": "", "directory": "packages/effects/plugins"}, "license": "MIT", "type": "module", "sideEffects": ["**/*.css"], "exports": {"./echarts": {"types": "./src/echarts/index.ts", "default": "./src/echarts/index.ts"}, "./vxe-table": {"types": "./src/vxe-table/index.ts", "default": "./src/vxe-table/index.ts"}, "./motion": {"types": "./src/motion/index.ts", "default": "./src/motion/index.ts"}, "./markmap": {"types": "./src/markmap/index.ts", "default": "./src/markmap/index.ts"}}, "dependencies": {"@vben-core/form-ui": "workspace:*", "@vben-core/shadcn-ui": "workspace:*", "@vben-core/shared": "workspace:*", "@vben/hooks": "workspace:*", "@vben/icons": "workspace:*", "@vben/locales": "workspace:*", "@vben/preferences": "workspace:*", "@vben/types": "workspace:*", "@vben/utils": "workspace:*", "@vueuse/core": "catalog:", "@vueuse/motion": "catalog:", "echarts": "catalog:", "markdown-it": "catalog:", "markmap-common": "catalog:", "markmap-lib": "catalog:", "markmap-toolbar": "catalog:", "markmap-view": "catalog:", "vue": "catalog:", "vxe-pc-ui": "catalog:", "vxe-table": "catalog:"}, "devDependencies": {"@types/markdown-it": "catalog:"}}