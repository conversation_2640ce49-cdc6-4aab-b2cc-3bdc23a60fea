{"name": "@vben/stores", "version": "5.5.7", "homepage": "", "bugs": "", "repository": {"type": "git", "url": "", "directory": "packages/stores"}, "license": "MIT", "type": "module", "sideEffects": ["**/*.css"], "exports": {".": {"types": "./src/index.ts", "default": "./src/index.ts"}}, "dependencies": {"@vben-core/preferences": "workspace:*", "@vben-core/shared": "workspace:*", "@vben-core/typings": "workspace:*", "pinia": "catalog:", "pinia-plugin-persistedstate": "catalog:", "secure-ls": "catalog:", "vue": "catalog:", "vue-router": "catalog:"}}