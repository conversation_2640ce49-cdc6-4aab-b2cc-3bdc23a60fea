{"name": "@vben/web-antd", "version": "5.5.7", "homepage": "", "bugs": "", "repository": {"type": "git", "url": "", "directory": "apps/web-antd"}, "license": "MIT", "author": {"name": "vben", "email": "<EMAIL>", "url": ""}, "type": "module", "scripts": {"build": "pnpm vite build --mode production", "build:analyze": "pnpm vite build --mode analyze", "dev": "pnpm vite --mode development", "preview": "vite preview", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>"}, "imports": {"#/*": "./src/*"}, "dependencies": {"@form-create/ant-design-vue": "catalog:", "@form-create/antd-designer": "catalog:", "@tinymce/tinymce-vue": "catalog:", "@vben/access": "workspace:*", "@vben/common-ui": "workspace:*", "@vben/constants": "workspace:*", "@vben/hooks": "workspace:*", "@vben/icons": "workspace:*", "@vben/layouts": "workspace:*", "@vben/locales": "workspace:*", "@vben/plugins": "workspace:*", "@vben/preferences": "workspace:*", "@vben/request": "workspace:*", "@vben/stores": "workspace:*", "@vben/styles": "workspace:*", "@vben/types": "workspace:*", "@vben/utils": "workspace:*", "@vueuse/core": "catalog:", "@vueuse/integrations": "catalog:", "ant-design-vue": "catalog:", "cropperjs": "catalog:", "crypto-js": "catalog:", "dayjs": "catalog:", "highlight.js": "catalog:", "pinia": "catalog:", "vue": "catalog:", "vue-dompurify-html": "catalog:", "vue-router": "catalog:", "vue3-signature": "catalog:"}, "devDependencies": {"@types/crypto-js": "catalog:"}}