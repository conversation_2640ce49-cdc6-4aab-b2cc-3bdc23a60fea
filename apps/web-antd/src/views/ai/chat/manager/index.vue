<script lang="ts" setup>
import { ref } from 'vue';

import { <PERSON><PERSON><PERSON><PERSON>, Page } from '@vben/common-ui';

import { Card, Tabs } from 'ant-design-vue';

import ChatConversationList from './modules/ChatConversationList.vue';
import ChatMessageList from './modules/ChatMessageList.vue';

const activeTabName = ref('conversation');
</script>

<template>
  <Page auto-content-height>
    <Card>
      <Tabs v-model:active-key="activeTabName">
        <Tabs.TabPane tab="对话列表" key="conversation">
          <ChatConversationList />
        </Tabs.TabPane>
        <Tabs.TabPane tab="消息列表" key="message">
          <ChatMessageList />
        </Tabs.TabPane>
      </Tabs>
    </Card>
  </Page>
</template>
